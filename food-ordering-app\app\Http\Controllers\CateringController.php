<?php

namespace App\Http\Controllers;

use App\Models\CateringEventType;
use App\Models\CateringPackage;
use App\Models\CateringOrder;
use App\Models\FoodItem;
use App\Models\Category;
use App\Models\Cuisine;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Session;

class CateringController extends Controller
{
    /**
     * Display the catering landing page.
     */
    public function index()
    {
        $eventTypes = CateringEventType::active()->ordered()->get();
        $popularPackages = CateringPackage::active()->popular()->ordered()->take(3)->get();
        
        return view('catering.index', compact('eventTypes', 'popularPackages'));
    }

    /**
     * Show the catering booking form (Step 1).
     */
    public function booking(Request $request)
    {
        $eventTypes = CateringEventType::active()->ordered()->get();

        // Pre-fill form if parameters are provided or from session
        $selectedEventType = $request->get('event_type') ?? Session::get('catering_step1.catering_event_type_id');
        $guestCount = $request->get('guests') ?? Session::get('catering_step1.guest_count');
        $eventDate = Session::get('catering_step1.event_date');
        $additionalServices = [
            'needs_setup_service' => Session::get('catering_step1.needs_setup_service', false),
            'needs_cleanup_service' => Session::get('catering_step1.needs_cleanup_service', false),
            'needs_serving_staff' => Session::get('catering_step1.needs_serving_staff', false),
            'serving_staff_count' => Session::get('catering_step1.serving_staff_count', 0),
        ];

        return view('catering.booking', compact(
            'eventTypes',
            'selectedEventType',
            'guestCount',
            'eventDate',
            'additionalServices'
        ));
    }

    /**
     * Process Step 1 and redirect to Step 2.
     */
    public function processStep1(Request $request)
    {
        $validated = $request->validate([
            'catering_event_type_id' => 'required|exists:catering_event_types,id',
            'event_date' => 'required|date|after:today',
            'guest_count' => 'required|integer|min:1|max:1000',
            'needs_setup_service' => 'boolean',
            'needs_cleanup_service' => 'boolean',
            'needs_serving_staff' => 'boolean',
            'serving_staff_count' => 'nullable|integer|min:0|max:20',
        ]);

        // Store step 1 data in session
        Session::put('catering_step1', $validated);

        return redirect()->route('catering.booking.step2');
    }

    /**
     * Show Step 2: Food Selection.
     */
    public function step2(Request $request)
    {
        // Check if step 1 data exists
        if (!Session::has('catering_step1')) {
            return redirect()->route('catering.booking')
                ->with('error', 'Please complete the event information first.');
        }

        $step1Data = Session::get('catering_step1');
        $eventType = CateringEventType::find($step1Data['catering_event_type_id']);

        // Get categories for filtering
        $categories = Category::active()->mainCategories()->ordered()->get();
        $cuisines = Cuisine::active()->ordered()->get();

        // Get selected food items from session
        $selectedFoodItems = Session::get('catering_selected_items', []);

        return view('catering.step2', compact(
            'step1Data',
            'eventType',
            'categories',
            'cuisines',
            'selectedFoodItems'
        ));
    }

    /**
     * Process Step 2 and create the order.
     */
    public function processStep2(Request $request)
    {
        $validated = $request->validate([
            'selected_food_items' => 'required|array|min:1',
            'selected_food_items.*' => 'exists:food_items,id',
            'food_item_quantities' => 'required|array',
            'food_item_quantities.*' => 'integer|min:1|max:100',
        ]);

        // Store step 2 data in session
        Session::put('catering_step2', $validated);

        // Redirect to order creation
        return redirect()->route('catering.order.store');
    }

    /**
     * Get packages available for a specific event type and guest count.
     */
    public function getAvailablePackages(Request $request): JsonResponse
    {
        $eventTypeId = $request->get('event_type_id');
        $guestCount = (int) $request->get('guest_count', 0);

        $packages = CateringPackage::active()
            ->where(function ($query) use ($guestCount) {
                if ($guestCount > 0) {
                    $query->where('min_guests', '<=', $guestCount)
                          ->where('max_guests', '>=', $guestCount);
                }
            })
            ->ordered()
            ->get();

        return response()->json([
            'packages' => $packages->map(function ($package) use ($guestCount) {
                $pricing = $guestCount > 0 ? $package->calculateTotalPrice($guestCount) : null;
                
                return [
                    'id' => $package->id,
                    'name' => $package->name,
                    'slug' => $package->slug,
                    'short_description' => $package->short_description,
                    'image_url' => $package->image_url,
                    'base_price_per_person' => $package->base_price_per_person,
                    'min_guests' => $package->min_guests,
                    'max_guests' => $package->max_guests,
                    'dietary_options' => $package->dietary_options,
                    'service_includes' => $package->service_includes,
                    'is_popular' => $package->is_popular,
                    'pricing' => $pricing,
                ];
            })
        ]);
    }

    /**
     * Calculate pricing for a specific package and guest count.
     */
    public function calculatePricing(Request $request): JsonResponse
    {
        $request->validate([
            'package_id' => 'required|exists:catering_packages,id',
            'guest_count' => 'required|integer|min:1',
            'serving_staff_count' => 'nullable|integer|min:0',
            'needs_setup_service' => 'boolean',
            'needs_cleanup_service' => 'boolean',
        ]);

        $package = CateringPackage::findOrFail($request->package_id);
        $guestCount = $request->guest_count;
        
        $options = [
            'serving_staff_count' => $request->get('serving_staff_count', 0),
            'needs_setup_service' => $request->boolean('needs_setup_service'),
            'needs_cleanup_service' => $request->boolean('needs_cleanup_service'),
        ];

        if (!$package->isAvailableForGuests($guestCount)) {
            return response()->json([
                'error' => "This package is only available for {$package->min_guests}-{$package->max_guests} guests."
            ], 400);
        }

        $pricing = $package->calculateTotalPrice($guestCount, $options);

        return response()->json([
            'success' => true,
            'pricing' => $pricing,
            'package' => [
                'name' => $package->name,
                'preparation_time_hours' => $package->preparation_time_hours,
            ]
        ]);
    }

    /**
     * Check availability for a specific date and time.
     */
    public function checkAvailability(Request $request): JsonResponse
    {
        $request->validate([
            'event_date' => 'required|date|after:today',
            'event_time' => 'required|date_format:H:i',
            'event_type_id' => 'required|exists:catering_event_types,id',
        ]);

        $eventType = CateringEventType::findOrFail($request->event_type_id);
        $eventDateTime = \Carbon\Carbon::createFromFormat(
            'Y-m-d H:i', 
            $request->event_date . ' ' . $request->event_time
        );

        // Check if the date/time meets minimum advance booking requirements
        if (!$eventType->isDateTimeAvailable($eventDateTime)) {
            $minTime = $eventType->getMinimumBookingTime();
            return response()->json([
                'available' => false,
                'message' => "This event type requires at least {$eventType->advance_booking_hours} hours advance booking. Earliest available time: " . $minTime->format('M j, Y g:i A')
            ]);
        }

        // Check for existing bookings (simplified - you might want more complex logic)
        $existingOrders = CateringOrder::where('event_date', $request->event_date)
            ->where('event_start_time', $request->event_time)
            ->whereNotIn('status', ['cancelled'])
            ->count();

        // Assume we can handle up to 5 events at the same time (adjust as needed)
        $maxConcurrentEvents = 5;
        $available = $existingOrders < $maxConcurrentEvents;

        return response()->json([
            'available' => $available,
            'message' => $available ? 'Time slot is available' : 'This time slot is fully booked. Please choose a different time.',
            'existing_bookings' => $existingOrders,
        ]);
    }

    /**
     * Get food items with filtering for Step 2.
     */
    public function getFoodItems(Request $request): JsonResponse
    {
        $query = FoodItem::available()->with(['category', 'cuisine']);

        // Apply filters
        if ($request->filled('service_tier')) {
            $tier = $request->get('service_tier');
            switch ($tier) {
                case 'premium':
                    $query->where('is_featured', true);
                    break;
                case 'deluxe':
                    $query->where('is_popular', true);
                    break;
                case 'classic':
                    $query->where('is_featured', false)->where('is_popular', false);
                    break;
            }
        }

        if ($request->filled('meal_type')) {
            $mealType = $request->get('meal_type');
            // You can add meal_type field to food_items table or use categories
            // For now, we'll use category names to determine meal types
            $mealCategories = [
                'breakfast' => ['Breakfast', 'Morning Snacks'],
                'lunch' => ['Main Course', 'Rice', 'Curry'],
                'tea' => ['Snacks', 'Tea Time', 'Beverages'],
                'dinner' => ['Main Course', 'Rice', 'Curry', 'Desserts']
            ];

            if (isset($mealCategories[$mealType])) {
                $query->whereHas('category', function ($q) use ($mealCategories, $mealType) {
                    $q->whereIn('name', $mealCategories[$mealType]);
                });
            }
        }

        if ($request->filled('dietary')) {
            $dietary = $request->get('dietary');
            if ($dietary === 'veg') {
                $query->where('is_vegetarian', true);
            } elseif ($dietary === 'non-veg') {
                $query->where('is_vegetarian', false);
            }
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->get('category_id'));
        }

        if ($request->filled('search')) {
            $query->search($request->get('search'));
        }

        $foodItems = $query->orderBy('sort_order')
                          ->orderBy('name')
                          ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $foodItems->items(),
            'pagination' => [
                'current_page' => $foodItems->currentPage(),
                'last_page' => $foodItems->lastPage(),
                'per_page' => $foodItems->perPage(),
                'total' => $foodItems->total(),
            ]
        ]);
    }

    /**
     * Show package details.
     */
    public function showPackage(CateringPackage $package)
    {
        $package->load('pricingTiers');

        return view('catering.package', compact('package'));
    }
}

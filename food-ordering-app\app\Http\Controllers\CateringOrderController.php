<?php

namespace App\Http\Controllers;

use App\Models\CateringOrder;
use App\Models\CateringEventType;
use App\Models\CateringPackage;
use App\Models\CateringOrderItem;
use App\Models\FoodItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;

class CateringOrderController extends Controller
{
    /**
     * Store a new catering order.
     */
    public function store(Request $request)
    {
        // Check if we have session data from multi-step process
        $step1Data = Session::get('catering_step1');
        $step2Data = Session::get('catering_step2');

        if (!$step1Data) {
            return redirect()->route('catering.booking')
                ->with('error', 'Please complete the booking process from the beginning.');
        }

        // If coming from multi-step process, use session data
        if ($step1Data && $step2Data) {
            $validatedData = $step1Data;
            $selectedFoodItems = $step2Data['selected_food_items'];
            $foodItemQuantities = $step2Data['food_item_quantities'];
        } else {
            // Fallback to direct form submission (backward compatibility)
            $validator = Validator::make($request->all(), [
                // Event Details
                'catering_event_type_id' => 'required|exists:catering_event_types,id',
                'event_date' => 'required|date|after:today',
                'guest_count' => 'required|integer|min:1|max:1000',

                // Additional Services
                'needs_setup_service' => 'boolean',
                'needs_cleanup_service' => 'boolean',
                'needs_serving_staff' => 'boolean',
                'serving_staff_count' => 'nullable|integer|min:0|max:20',
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            }

            $validatedData = $validator->validated();
            $selectedFoodItems = [];
            $foodItemQuantities = [];
        }

        try {
            DB::beginTransaction();

            // Validate event type
            $eventType = CateringEventType::findOrFail($validatedData['catering_event_type_id']);

            // Check guest count limits
            if ($validatedData['guest_count'] < $eventType->min_guests || $validatedData['guest_count'] > $eventType->max_guests) {
                return back()->withErrors([
                    'guest_count' => "This event type is only available for {$eventType->min_guests}-{$eventType->max_guests} guests."
                ])->withInput();
            }

            // Calculate basic pricing based on event type
            $baseAmount = $eventType->base_price_per_person * $validatedData['guest_count'];
            $setupFee = ($validatedData['needs_setup_service'] ?? false) ? 50.00 : 0;
            $cleanupFee = ($validatedData['needs_cleanup_service'] ?? false) ? 30.00 : 0;
            $staffFee = ($validatedData['needs_serving_staff'] ?? false) ? (($validatedData['serving_staff_count'] ?? 0) * 25.00) : 0;

            // Calculate food items cost
            $foodItemsCost = 0;
            if (!empty($selectedFoodItems)) {
                $foodItems = FoodItem::whereIn('id', $selectedFoodItems)->get();
                foreach ($foodItems as $foodItem) {
                    $quantity = $foodItemQuantities[$foodItem->id] ?? 1;
                    $foodItemsCost += $foodItem->price_per_unit * $quantity;
                }
            }

            $serviceFee = ($baseAmount + $foodItemsCost) * 0.10; // 10% service fee
            $taxAmount = ($baseAmount + $foodItemsCost + $setupFee + $cleanupFee + $staffFee + $serviceFee) * 0.08; // 8% tax
            $totalAmount = $baseAmount + $foodItemsCost + $setupFee + $cleanupFee + $staffFee + $serviceFee + $taxAmount;

            // Create the catering order
            $cateringOrder = CateringOrder::create([
                'order_number' => CateringOrder::generateOrderNumber(),
                'user_id' => auth()->id(),
                'catering_event_type_id' => $validatedData['catering_event_type_id'],
                'catering_package_id' => null, // No package selection in simplified form
                'customer_name' => 'Guest', // Default since not collected
                'customer_email' => '<EMAIL>', // Default since not collected
                'customer_phone' => '************', // Default since not collected
                'company_name' => null,
                'event_name' => 'Catering Event', // Default since not collected
                'event_date' => $validatedData['event_date'],
                'event_start_time' => '12:00:00', // Default since not collected
                'event_end_time' => null,
                'guest_count' => $validatedData['guest_count'],
                'event_description' => null,
                'event_address' => 'TBD', // Default since not collected
                'event_city' => 'TBD', // Default since not collected
                'event_state' => null,
                'event_postal_code' => '00000', // Default since not collected
                'venue_details' => null,
                'dietary_requirements' => null,
                'special_requests' => null,
                'needs_setup_service' => $validatedData['needs_setup_service'] ?? false,
                'needs_cleanup_service' => $validatedData['needs_cleanup_service'] ?? false,
                'needs_serving_staff' => $validatedData['needs_serving_staff'] ?? false,
                'serving_staff_count' => $validatedData['serving_staff_count'] ?? 0,
                'base_amount' => $baseAmount + $foodItemsCost,
                'setup_fee' => $setupFee + $cleanupFee, // Combined setup and cleanup fees
                'service_fee' => $serviceFee,
                'delivery_fee' => 0,
                'staff_fee' => $staffFee,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'payment_method' => 'pending', // Default since not collected
                'status' => 'pending',
                'payment_status' => 'pending',
            ]);

            // Create a basic order item for the catering service
            CateringOrderItem::create([
                'catering_order_id' => $cateringOrder->id,
                'item_type' => 'catering_service',
                'item_name' => $eventType->name . ' Catering Service',
                'item_description' => 'Basic catering service for ' . $validatedData['guest_count'] . ' guests',
                'quantity' => 1,
                'unit_price' => $baseAmount,
                'total_price' => $baseAmount,
            ]);

            // Create order items for selected food items
            if (!empty($selectedFoodItems)) {
                $foodItems = FoodItem::whereIn('id', $selectedFoodItems)->get();
                foreach ($foodItems as $foodItem) {
                    $quantity = $foodItemQuantities[$foodItem->id] ?? 1;
                    CateringOrderItem::create([
                        'catering_order_id' => $cateringOrder->id,
                        'item_type' => 'food_item',
                        'item_name' => $foodItem->name,
                        'item_description' => $foodItem->description,
                        'quantity' => $quantity,
                        'unit_price' => $foodItem->price_per_unit,
                        'total_price' => $foodItem->price_per_unit * $quantity,
                    ]);
                }
            }

            DB::commit();

            // Clear session data
            Session::forget(['catering_step1', 'catering_step2', 'catering_selected_items']);

            return redirect()->route('catering.confirmation', $cateringOrder->order_number)
                ->with('success', 'Your catering order has been submitted successfully!');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withErrors([
                'general' => 'An error occurred while processing your order. Please try again.'
            ])->withInput();
        }
    }

    /**
     * Show order confirmation page.
     */
    public function confirmation(CateringOrder $cateringOrder)
    {
        $cateringOrder->load(['eventType', 'package', 'orderItems']);
        
        return view('catering.confirmation', compact('cateringOrder'));
    }

    /**
     * Show order tracking page.
     */
    public function track(CateringOrder $cateringOrder)
    {
        $cateringOrder->load(['eventType', 'package', 'orderItems']);
        
        return view('catering.track', compact('cateringOrder'));
    }

    /**
     * Display a listing of catering orders (admin).
     */
    public function index(Request $request)
    {
        $query = CateringOrder::with(['eventType', 'package'])
            ->orderBy('event_date', 'desc')
            ->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('event_date', '>=', $request->date_from);
        }
        
        if ($request->filled('date_to')) {
            $query->where('event_date', '<=', $request->date_to);
        }

        $orders = $query->paginate(20);

        return view('catering.admin.index', compact('orders'));
    }

    /**
     * Show the specified catering order (admin).
     */
    public function show(CateringOrder $cateringOrder)
    {
        $cateringOrder->load(['eventType', 'package', 'orderItems', 'user']);
        
        return view('catering.admin.show', compact('cateringOrder'));
    }

    /**
     * Update the order status.
     */
    public function updateStatus(Request $request, CateringOrder $cateringOrder)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,in_preparation,ready,in_transit,delivered,completed,cancelled',
            'notes' => 'nullable|string|max:1000',
        ]);

        $cateringOrder->updateStatus($request->status, $request->notes);

        return back()->with('success', 'Order status updated successfully.');
    }
}

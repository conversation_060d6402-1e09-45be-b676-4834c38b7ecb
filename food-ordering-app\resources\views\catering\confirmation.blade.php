@extends('layouts.app')

@section('title', 'Catering Order Confirmation - Food Ordering App')

@section('content')
<div class="container-mobile py-6">
    <div class="max-w-4xl mx-auto">
        <!-- Success Message -->
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
            <div class="flex items-center">
                <i class="fas fa-check-circle text-xl mr-3"></i>
                <div>
                    <h2 class="font-semibold">Catering Order Submitted Successfully!</h2>
                    <p class="text-sm">We'll contact you within 24 hours to confirm your order details.</p>
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-2xl font-bold text-gray-800">Order Confirmation</h1>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Order Number</p>
                    <p class="text-lg font-semibold text-orange-600">{{ $cateringOrder->order_number }}</p>
                </div>
            </div>

            <!-- Event Details -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">Event Details</h3>
                    <div class="space-y-2 text-sm">
                        @if($cateringOrder->event_name)
                        <div class="flex">
                            <span class="font-medium text-gray-600 w-24">Event:</span>
                            <span>{{ $cateringOrder->event_name }}</span>
                        </div>
                        @endif
                        <div class="flex">
                            <span class="font-medium text-gray-600 w-24">Type:</span>
                            <span>{{ $cateringOrder->eventType->name }}</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-600 w-24">Date:</span>
                            <span>{{ $cateringOrder->event_date->format('M j, Y') }}</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-600 w-24">Time:</span>
                            <span>{{ $cateringOrder->event_start_time->format('g:i A') }}
                                @if($cateringOrder->event_end_time)
                                    - {{ $cateringOrder->event_end_time->format('g:i A') }}
                                @endif
                            </span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-600 w-24">Guests:</span>
                            <span>{{ $cateringOrder->guest_count }} people</span>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Additional Services -->
            @if($cateringOrder->needs_setup_service || $cateringOrder->needs_cleanup_service || $cateringOrder->needs_serving_staff)
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Additional Services</h3>
                <div class="space-y-2 text-sm">
                    @if($cateringOrder->needs_setup_service)
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-600 mr-2"></i>
                        <span>Setup Service</span>
                    </div>
                    @endif
                    @if($cateringOrder->needs_cleanup_service)
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-600 mr-2"></i>
                        <span>Cleanup Service</span>
                    </div>
                    @endif
                    @if($cateringOrder->needs_serving_staff)
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-600 mr-2"></i>
                        <span>Professional Serving Staff ({{ $cateringOrder->serving_staff_count }} staff)</span>
                    </div>
                    @endif
                </div>
            </div>
            @endif



            <!-- Pricing Summary -->
            <div class="border-t pt-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Pricing Summary</h3>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Base Amount ({{ $cateringOrder->guest_count }} guests):</span>
                            <span>${{ number_format($cateringOrder->base_amount, 2) }}</span>
                        </div>
                        @if($cateringOrder->setup_fee > 0)
                        <div class="flex justify-between">
                            <span>Setup Fee:</span>
                            <span>${{ number_format($cateringOrder->setup_fee, 2) }}</span>
                        </div>
                        @endif
                        @if($cateringOrder->service_fee > 0)
                        <div class="flex justify-between">
                            <span>Service Fee:</span>
                            <span>${{ number_format($cateringOrder->service_fee, 2) }}</span>
                        </div>
                        @endif
                        @if($cateringOrder->delivery_fee > 0)
                        <div class="flex justify-between">
                            <span>Delivery Fee:</span>
                            <span>${{ number_format($cateringOrder->delivery_fee, 2) }}</span>
                        </div>
                        @endif
                        @if($cateringOrder->staff_fee > 0)
                        <div class="flex justify-between">
                            <span>Staff Fee:</span>
                            <span>${{ number_format($cateringOrder->staff_fee, 2) }}</span>
                        </div>
                        @endif
                        <div class="flex justify-between">
                            <span>Tax:</span>
                            <span>${{ number_format($cateringOrder->tax_amount, 2) }}</span>
                        </div>
                        <div class="border-t pt-2 flex justify-between font-bold text-lg">
                            <span>Total Amount:</span>
                            <span class="text-orange-600">${{ number_format($cateringOrder->total_amount, 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-blue-800 mb-3">What Happens Next?</h3>
            <div class="space-y-2 text-sm text-blue-700">
                <div class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
                    <span>Our catering team will review your order and contact you within 24 hours to confirm details.</span>
                </div>
                <div class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
                    <span>We'll finalize the menu, timing, and any special requirements with you.</span>
                </div>
                <div class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
                    <span>Payment arrangements will be discussed and confirmed.</span>
                </div>
                <div class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">4</span>
                    <span>Your delicious catering will be prepared and delivered on time for your event!</span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('catering.track', $cateringOrder->order_number) }}" 
               class="bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors text-center">
                Track Your Order
            </a>
            <a href="{{ route('catering.index') }}" 
               class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors text-center">
                Book Another Event
            </a>
            <a href="{{ route('home') }}" 
               class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors text-center">
                Back to Home
            </a>
        </div>
    </div>
</div>
@endsection

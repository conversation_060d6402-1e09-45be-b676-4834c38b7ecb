<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title>Food Selection - Catering Service</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        orange: {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                        }
                    },
                    fontFamily: {
                        sans: ['Figtree', 'ui-sans-serif', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>


<style>
    /* Use existing food app styles */
    :root {
        --primary-orange: #ea580c;
        --secondary-orange: #fb923c;
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --border-radius: 0.5rem;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --transition: all 0.2s ease-in-out;
    }

    /* Filter Navigation Styles */
    .filter-nav-container {
        background: white;
        border-bottom: 1px solid var(--gray-200);
        position: sticky;
        top: 0;
        z-index: 30;
        padding: 1rem 0;
    }

    .filter-rows {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .filter-row {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex-wrap: nowrap;
    }

    .filter-row-label {
        font-weight: 600;
        color: var(--gray-700);
        min-width: 80px;
        font-size: 0.875rem;
        flex-shrink: 0;
    }

    /* Filter button containers */
    .filter-row .flex {
        align-items: center;
        gap: 0.5rem;
    }

    .filter-row .flex::-webkit-scrollbar {
        height: 2px;
    }

    .filter-row .flex::-webkit-scrollbar-track {
        background: transparent;
    }

    .filter-row .flex::-webkit-scrollbar-thumb {
        background: var(--gray-300);
        border-radius: 1px;
    }

    /* Service Tier Buttons */
    .service-tier-btn {
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.875rem;
        border: 2px solid transparent;
        cursor: pointer;
        transition: var(--transition);
        flex: 1;
        text-align: center;
        min-width: 100px;
    }

    .service-tier-btn.premium {
        background: linear-gradient(135deg, #fbbf24, #f59e0b);
        color: white;
        border-color: #f59e0b;
    }

    .service-tier-btn.deluxe {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        color: white;
        border-color: #7c3aed;
    }

    .service-tier-btn.classic {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        border-color: #059669;
    }

    .service-tier-btn.active {
        transform: scale(1.05);
        box-shadow: var(--shadow-lg);
    }

    /* Meal Type Buttons */
    .meal-type-btn {
        padding: 0.625rem 1.25rem;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.875rem;
        background: var(--gray-100);
        color: var(--gray-700);
        border: 1px solid var(--gray-300);
        cursor: pointer;
        transition: var(--transition);
        flex: 1;
        text-align: center;
        min-width: 80px;
    }

    .meal-type-btn.active {
        background: var(--primary-orange);
        color: white;
        border-color: var(--primary-orange);
    }

    /* Dietary Buttons */
    .diet-filter-btn {
        padding: 0.625rem 1.25rem;
        border-radius: 15px;
        font-weight: 500;
        font-size: 0.875rem;
        background: var(--gray-100);
        color: var(--gray-700);
        border: 1px solid var(--gray-300);
        cursor: pointer;
        transition: var(--transition);
        min-width: 80px;
        text-align: center;
    }

    .diet-filter-btn.veg.active {
        background: #10b981;
        color: white;
        border-color: #10b981;
    }

    .diet-filter-btn.non-veg.active {
        background: #ef4444;
        color: white;
        border-color: #ef4444;
    }

    /* Search Input */
    .search-input {
        flex: 1;
        padding: 0.625rem 1rem;
        border: 1px solid var(--gray-300);
        border-radius: 0.5rem;
        font-size: 0.875rem;
        min-width: 200px;
    }

    .search-btn {
        padding: 0.625rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
    }

    /* Category Slider */
    .category-slider {
        background: white;
        border-bottom: 1px solid var(--gray-200);
        position: sticky;
        top: 140px;
        z-index: 25;
        padding: 0.75rem 0;
    }

    .category-btn {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.875rem;
        background: var(--gray-100);
        color: var(--gray-700);
        border: 1px solid var(--gray-300);
        cursor: pointer;
        transition: var(--transition);
        white-space: nowrap;
        min-width: 100px;
        text-align: center;
        flex-shrink: 0;
    }

    .category-btn.active {
        background: var(--primary-orange);
        color: white;
        border-color: var(--primary-orange);
    }

    /* Food Cards */
    .food-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
        overflow: hidden;
        border: 1px solid var(--gray-100);
        position: relative;
    }

    .food-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-orange);
    }

    .food-card.selected {
        border-color: var(--primary-orange);
        box-shadow: var(--shadow-lg);
    }

    .food-card img {
        transition: var(--transition);
    }

    .food-card:hover img {
        transform: scale(1.05);
    }

    /* Selected Items Counter */
    .selected-counter {
        position: fixed;
        bottom: 100px;
        right: 1rem;
        background: var(--primary-orange);
        color: white;
        padding: 1rem;
        border-radius: 50px;
        box-shadow: var(--shadow-xl);
        z-index: 50;
        min-width: 120px;
        text-align: center;
    }

    /* Loading Spinner */
    .spinner {
        border: 2px solid #f3f3f3;
        border-top: 2px solid var(--primary-orange);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Header without navbar */
    .header-no-nav {
        position: relative;
        z-index: 40;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .filter-nav-container {
            top: 0;
            padding: 0.75rem 0;
        }

        .header-no-nav .container-mobile {
            padding: 1rem;
        }

        .header-no-nav h1 {
            font-size: 1.5rem;
        }

        .header-no-nav .w-10 {
            width: 2rem;
            height: 2rem;
        }

        .header-no-nav .w-24 {
            width: 4rem;
        }

        .filter-rows {
            gap: 0.75rem;
        }

        .filter-row {
            gap: 0.5rem;
            flex-wrap: nowrap;
        }

        .filter-row-label {
            flex-shrink: 0;
            min-width: 60px;
            font-size: 0.75rem;
        }

        .filter-row .flex {
            gap: 0.375rem;
            overflow-x: auto;
            padding-bottom: 0.25rem;
        }

        .service-tier-btn {
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
            min-width: 70px;
            flex-shrink: 0; /* Prevent buttons from shrinking */
        }

        .meal-type-btn {
            padding: 0.5rem 0.625rem;
            font-size: 0.75rem;
            min-width: 55px;
            flex-shrink: 0;
        }

        .diet-filter-btn {
            padding: 0.5rem 0.625rem;
            font-size: 0.75rem;
            min-width: 50px;
            flex-shrink: 0;
        }

        .search-input {
            min-width: 120px;
            flex-shrink: 1; /* Allow search to shrink if needed */
        }

        .search-btn {
            flex-shrink: 0;
            padding: 0.5rem 0.75rem;
        }

        .category-slider {
            top: 140px;
        }

        .category-btn {
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
            min-width: 80px;
        }

        #category-slider {
            gap: 0.5rem;
        }


    }

    @media (max-width: 480px) {
        .filter-nav-container {
            padding: 0.5rem 0;
        }

        .filter-rows {
            gap: 0.5rem;
        }

        .filter-row {
            gap: 0.375rem;
            flex-wrap: nowrap;
        }

        .filter-row-label {
            min-width: 50px;
            font-size: 0.625rem;
            font-weight: 600;
        }

        .filter-row .flex {
            gap: 0.25rem;
            overflow-x: auto;
            padding-bottom: 0.125rem;
        }

        .service-tier-btn {
            padding: 0.375rem 0.5rem;
            font-size: 0.625rem;
            min-width: 60px;
            border-radius: 15px;
        }

        .meal-type-btn {
            padding: 0.375rem 0.5rem;
            font-size: 0.625rem;
            min-width: 45px;
            border-radius: 12px;
        }

        .diet-filter-btn {
            padding: 0.375rem 0.5rem;
            font-size: 0.625rem;
            min-width: 40px;
            border-radius: 10px;
        }

        .search-input {
            min-width: 100px;
            padding: 0.375rem 0.5rem;
            font-size: 0.75rem;
        }

        .search-btn {
            padding: 0.375rem 0.5rem;
            font-size: 0.75rem;
        }

        .category-slider {
            top: 120px;
        }

        .category-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.625rem;
            min-width: 60px;
        }

        #category-slider {
            gap: 0.375rem;
        }

        /* Ensure buttons don't wrap to new lines */
        .service-tier-btn,
        .meal-type-btn,
        .diet-filter-btn,
        .category-btn {
            white-space: nowrap;
            flex-shrink: 0;
        }
    }

    /* Mobile-first responsive design */
    .container-mobile {
        max-width: 100%;
        padding: 0 1rem;
    }

    @media (min-width: 640px) {
        .container-mobile {
            max-width: 640px;
            margin: 0 auto;
        }
    }

    @media (min-width: 768px) {
        .container-mobile {
            max-width: 768px;
        }
    }

    @media (min-width: 1024px) {
        .container-mobile {
            max-width: 1024px;
        }
    }

    /* Additional utility classes */
    .line-clamp-2 {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }

    .min-w-0 {
        min-width: 0;
    }

    .flex-nowrap {
        flex-wrap: nowrap;
    }

    .flex-shrink-0 {
        flex-shrink: 0;
    }

    .overflow-x-auto {
        overflow-x: auto;
    }
</style>
</head>

<body class="bg-gray-50 font-sans antialiased"
      style="margin: 0; padding: 0; min-height: 100vh;"
      x-data="{ mobileMenuOpen: false }"
      :class="{ 'overflow-hidden': mobileMenuOpen }">
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-lg border-b-2 border-orange-100 header-no-nav">
        <div class="container-mobile py-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-utensils text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-800">Food Selection</h1>
                            <span class="text-sm text-orange-600 font-medium">Step 2 of 2</span>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm"><?php echo e($eventType->name); ?> for <?php echo e($step1Data['guest_count']); ?> guests on <?php echo e(\Carbon\Carbon::parse($step1Data['event_date'])->format('M j, Y')); ?></p>
                </div>
                <div class="text-right">
                    <div class="w-24 h-3 bg-gray-200 rounded-full">
                        <div class="w-full h-3 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full"></div>
                    </div>
                    <span class="text-xs text-gray-500 mt-1 block">Complete</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Navigation -->
    <div class="filter-nav-container">
        <div class="container-mobile">
            <div class="filter-rows">
                <!-- First Row: Service Tiers -->
                <div class="filter-row">
                    <span class="filter-row-label">Service:</span>
                    <div class="flex gap-2 flex-1 overflow-x-auto">
                        <button class="service-tier-btn premium active" onclick="toggleServiceTier('premium')" id="premium-btn">
                            Premium
                        </button>
                        <button class="service-tier-btn deluxe" onclick="toggleServiceTier('deluxe')" id="deluxe-btn">
                            Deluxe
                        </button>
                        <button class="service-tier-btn classic" onclick="toggleServiceTier('classic')" id="classic-btn">
                            Classic
                        </button>
                    </div>
                </div>

                <!-- Second Row: Meal Types -->
                <div class="filter-row">
                    <span class="filter-row-label">Meal:</span>
                    <div class="flex gap-2 flex-1 overflow-x-auto">
                        <button class="meal-type-btn" onclick="toggleMealType('breakfast')" id="breakfast-btn">
                            Breakfast
                        </button>
                        <button class="meal-type-btn" onclick="toggleMealType('lunch')" id="lunch-btn">
                            Lunch
                        </button>
                        <button class="meal-type-btn" onclick="toggleMealType('tea')" id="tea-btn">
                            Tea
                        </button>
                        <button class="meal-type-btn" onclick="toggleMealType('dinner')" id="dinner-btn">
                            Dinner
                        </button>
                    </div>
                </div>

                <!-- Third Row: Dietary Options and Search -->
                <div class="filter-row">
                    <span class="filter-row-label">Diet:</span>
                    <div class="flex gap-2 flex-1 overflow-x-auto">
                        <button class="diet-filter-btn veg" onclick="toggleDietFilter('veg')" id="veg-btn">
                            Veg
                        </button>
                        <button class="diet-filter-btn non-veg" onclick="toggleDietFilter('non-veg')" id="non-veg-btn">
                            Non-Veg
                        </button>
                        <div class="flex items-center gap-1 flex-1 min-w-0">
                            <input type="text" class="search-input" placeholder="Search..." id="food-search" onkeypress="handleSearch(event)">
                            <button onclick="performSearch()" class="search-btn bg-orange-500 hover:bg-orange-600 text-white">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Slider -->
    <div class="category-slider">
        <div class="container-mobile">
            <div class="flex gap-3 overflow-x-auto pb-2 flex-nowrap" id="category-slider">
                <button class="category-btn active" onclick="toggleCategory('')" id="all-categories-btn">
                    All Categories
                </button>
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button class="category-btn" onclick="toggleCategory('<?php echo e($category->id); ?>')" id="category-<?php echo e($category->id); ?>-btn">
                    <?php echo e($category->name); ?>

                </button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Food Items Grid -->
    <div class="container-mobile py-6">
        <div id="loading-indicator" class="text-center py-8 hidden">
            <div class="spinner mx-auto mb-4"></div>
            <p class="text-gray-600">Loading food items...</p>
        </div>

        <div id="food-items-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <!-- Food items will be loaded here via JavaScript -->
        </div>

        <div id="no-results" class="text-center py-8 hidden">
            <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-600">No food items found matching your criteria.</p>
            <button onclick="clearFilters()" class="mt-4 bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600">
                Clear Filters
            </button>
        </div>

        <!-- Load More Button -->
        <div id="load-more-container" class="text-center mt-8 hidden">
            <button onclick="loadMoreItems()" id="load-more-btn" class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200">
                Load More Items
            </button>
        </div>
    </div>

    <!-- Selected Items Counter -->
    <div class="selected-counter" id="selected-counter" style="display: none;">
        <div class="font-semibold">Selected</div>
        <div class="text-lg" id="selected-count">0</div>
        <div class="text-xs">items</div>
    </div>

    <!-- Navigation Buttons -->
    <div class="bg-white border-t-2 border-orange-100 shadow-lg sticky bottom-0 z-40">
        <div class="container-mobile py-5">
            <form action="<?php echo e(route('catering.booking.step2.process')); ?>" method="POST" id="food-selection-form">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="selected_food_items" id="selected-food-items-input">
                <input type="hidden" name="food_item_quantities" id="food-item-quantities-input">

                <div class="flex justify-between items-center gap-4">
                    <a href="<?php echo e(route('catering.booking')); ?>" class="bg-gray-100 text-gray-700 px-6 py-4 rounded-xl font-semibold hover:bg-gray-200 transition-all duration-200 flex items-center gap-2 shadow-sm">
                        <i class="fas fa-arrow-left"></i>
                        <span class="hidden sm:inline">Previous Step</span>
                        <span class="sm:hidden">Back</span>
                    </a>
                    <button type="submit" id="submit-btn" class="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-orange-600 hover:to-orange-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg flex items-center gap-2 flex-1 justify-center max-w-xs" disabled>
                        <span>Complete Order</span>
                        <i class="fas fa-check"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Global variables
let currentFilters = {
    service_tier: 'premium',
    meal_type: '',
    dietary: '',
    category_id: '',
    search: ''
};

let selectedFoodItems = <?php echo json_encode($selectedFoodItems, 15, 512) ?>;
let currentPage = 1;
let lastPage = 1;
let isLoading = false;

$(document).ready(function() {
    loadFoodItems();
    updateSelectedCounter();
    updateSubmitButton();
});

// Service Tier Toggle
function toggleServiceTier(tier) {
    $('.service-tier-btn').removeClass('active');
    $('#' + tier + '-btn').addClass('active');
    currentFilters.service_tier = tier;
    resetPagination();
    loadFoodItems();
}

// Meal Type Toggle
function toggleMealType(type) {
    if (currentFilters.meal_type === type) {
        $('.meal-type-btn').removeClass('active');
        currentFilters.meal_type = '';
    } else {
        $('.meal-type-btn').removeClass('active');
        $('#' + type + '-btn').addClass('active');
        currentFilters.meal_type = type;
    }
    resetPagination();
    loadFoodItems();
}

// Diet Filter Toggle
function toggleDietFilter(diet) {
    if (currentFilters.dietary === diet) {
        $('.diet-filter-btn').removeClass('active');
        currentFilters.dietary = '';
    } else {
        $('.diet-filter-btn').removeClass('active');
        $('#' + diet + '-btn').addClass('active');
        currentFilters.dietary = diet;
    }
    resetPagination();
    loadFoodItems();
}

// Category Toggle
function toggleCategory(categoryId) {
    $('.category-btn').removeClass('active');
    if (categoryId === '') {
        $('#all-categories-btn').addClass('active');
        currentFilters.category_id = '';
    } else {
        $('#category-' + categoryId + '-btn').addClass('active');
        currentFilters.category_id = categoryId;
    }
    resetPagination();
    loadFoodItems();
}

// Search Functions
function handleSearch(event) {
    if (event.key === 'Enter') {
        performSearch();
    }
}

function performSearch() {
    currentFilters.search = $('#food-search').val();
    resetPagination();
    loadFoodItems();
}

// Clear Filters
function clearFilters() {
    currentFilters = {
        service_tier: 'premium',
        meal_type: '',
        dietary: '',
        category_id: '',
        search: ''
    };

    // Reset UI
    $('.service-tier-btn').removeClass('active');
    $('#premium-btn').addClass('active');
    $('.meal-type-btn').removeClass('active');
    $('.diet-filter-btn').removeClass('active');
    $('.category-btn').removeClass('active');
    $('#all-categories-btn').addClass('active');
    $('#food-search').val('');

    resetPagination();
    loadFoodItems();
}

// Reset Pagination
function resetPagination() {
    currentPage = 1;
    $('#food-items-grid').empty();
}

// Load Food Items
function loadFoodItems(append = false) {
    if (isLoading) return;

    isLoading = true;
    $('#loading-indicator').removeClass('hidden');
    $('#no-results').addClass('hidden');

    const params = {
        ...currentFilters,
        page: currentPage
    };

    $.get('<?php echo e(route("catering.api.food-items")); ?>', params)
        .done(function(response) {
            if (response.success) {
                const items = response.data;
                lastPage = response.pagination.last_page;

                if (items.length === 0 && currentPage === 1) {
                    $('#no-results').removeClass('hidden');
                    $('#load-more-container').addClass('hidden');
                } else {
                    $('#no-results').addClass('hidden');

                    if (append) {
                        appendFoodItems(items);
                    } else {
                        renderFoodItems(items);
                    }

                    // Show/hide load more button
                    if (currentPage < lastPage) {
                        $('#load-more-container').removeClass('hidden');
                    } else {
                        $('#load-more-container').addClass('hidden');
                    }
                }
            }
        })
        .fail(function() {
            showToast('Error loading food items', 'error');
        })
        .always(function() {
            isLoading = false;
            $('#loading-indicator').addClass('hidden');
        });
}

// Render Food Items
function renderFoodItems(items) {
    const grid = $('#food-items-grid');
    grid.empty();
    appendFoodItems(items);
}

// Append Food Items
function appendFoodItems(items) {
    const grid = $('#food-items-grid');

    items.forEach(function(item) {
        const isSelected = selectedFoodItems.hasOwnProperty(item.id);
        const quantity = selectedFoodItems[item.id] || 1;

        const itemHtml = `
            <div class="food-card ${isSelected ? 'selected' : ''}" data-item-id="${item.id}">
                <div class="relative">
                    <img src="${item.image_url || '/images/placeholder-food.jpg'}"
                         alt="${item.name}"
                         class="w-full h-32 object-cover">
                    ${item.is_vegetarian ? '<span class="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded">Veg</span>' : '<span class="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded">Non-Veg</span>'}
                    ${item.is_featured ? '<span class="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded">Premium</span>' : ''}
                </div>
                <div class="p-3">
                    <h3 class="font-semibold text-sm text-gray-800 mb-1">${item.name}</h3>
                    <p class="text-xs text-gray-600 mb-2 line-clamp-2">${item.description || ''}</p>
                    <div class="flex items-center justify-between">
                        <span class="font-bold text-orange-600">₹${item.price_per_unit}/unit</span>
                        <div class="flex items-center gap-2">
                            ${isSelected ? `
                                <div class="flex items-center gap-1">
                                    <button onclick="updateQuantity(${item.id}, -1)" class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs">-</button>
                                    <span class="text-sm font-medium w-6 text-center">${quantity}</span>
                                    <button onclick="updateQuantity(${item.id}, 1)" class="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-xs">+</button>
                                </div>
                            ` : `
                                <button onclick="addItem(${item.id})" class="bg-orange-500 text-white px-3 py-1 rounded text-xs hover:bg-orange-600">
                                    Add
                                </button>
                            `}
                        </div>
                    </div>
                </div>
            </div>
        `;

        grid.append(itemHtml);
    });
}

// Add Item
function addItem(itemId) {
    selectedFoodItems[itemId] = 1;
    updateSelectedCounter();
    updateSubmitButton();
    updateItemCard(itemId);
}

// Remove Item
function removeItem(itemId) {
    delete selectedFoodItems[itemId];
    updateSelectedCounter();
    updateSubmitButton();
    updateItemCard(itemId);
}

// Update Quantity
function updateQuantity(itemId, change) {
    if (selectedFoodItems[itemId]) {
        selectedFoodItems[itemId] += change;
        if (selectedFoodItems[itemId] <= 0) {
            removeItem(itemId);
        } else {
            updateSelectedCounter();
            updateItemCard(itemId);
        }
    }
}

// Update Item Card
function updateItemCard(itemId) {
    const card = $(`.food-card[data-item-id="${itemId}"]`);
    const isSelected = selectedFoodItems.hasOwnProperty(itemId);
    const quantity = selectedFoodItems[itemId] || 1;

    if (isSelected) {
        card.addClass('selected');
        const quantityControls = `
            <div class="flex items-center gap-1">
                <button onclick="updateQuantity(${itemId}, -1)" class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs">-</button>
                <span class="text-sm font-medium w-6 text-center">${quantity}</span>
                <button onclick="updateQuantity(${itemId}, 1)" class="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-xs">+</button>
            </div>
        `;
        card.find('.flex.items-center.gap-2').html(quantityControls);
    } else {
        card.removeClass('selected');
        const addButton = `
            <button onclick="addItem(${itemId})" class="bg-orange-500 text-white px-3 py-1 rounded text-xs hover:bg-orange-600">
                Add
            </button>
        `;
        card.find('.flex.items-center.gap-2').html(addButton);
    }
}

// Update Selected Counter
function updateSelectedCounter() {
    const count = Object.keys(selectedFoodItems).length;
    $('#selected-count').text(count);

    if (count > 0) {
        $('#selected-counter').show();
    } else {
        $('#selected-counter').hide();
    }
}

// Update Submit Button
function updateSubmitButton() {
    const count = Object.keys(selectedFoodItems).length;
    const submitBtn = $('#submit-btn');

    if (count > 0) {
        submitBtn.prop('disabled', false);
    } else {
        submitBtn.prop('disabled', true);
    }
}

// Load More Items
function loadMoreItems() {
    if (currentPage < lastPage) {
        currentPage++;
        loadFoodItems(true);
    }
}

// Form Submission
$('#food-selection-form').on('submit', function(e) {
    const selectedIds = Object.keys(selectedFoodItems);
    const quantities = selectedFoodItems;

    if (selectedIds.length === 0) {
        e.preventDefault();
        showToast('Please select at least one food item', 'error');
        return;
    }

    $('#selected-food-items-input').val(JSON.stringify(selectedIds));
    $('#food-item-quantities-input').val(JSON.stringify(quantities));
});

// Toast Function
function showToast(message, type = 'info') {
    // Simple toast implementation
    const toast = $(`
        <div class="fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'error' ? 'bg-red-500' : 'bg-green-500'}">
            ${message}
        </div>
    `);

    $('body').append(toast);

    setTimeout(function() {
        toast.fadeOut(function() {
            toast.remove();
        });
    }, 3000);
}
</script>

</body>
</html>
<?php /**PATH C:\Users\<USER>\Downloads\r56t7yihu\food-ordering-app\resources\views/catering/step2.blade.php ENDPATH**/ ?>
<?php $__env->startSection('title', 'Book Catering Service - Food Ordering App'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-mobile py-6">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Book Your Catering Service</h1>
            <p class="text-gray-600">Fill out the form below to get a personalized quote</p>
        </div>

        <!-- Progress Steps -->
        <div class="mb-8">
            <div class="flex items-center justify-center space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                    <span class="ml-2 text-sm font-medium text-orange-600">Event Details</span>
                </div>
                <div class="w-8 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                    <span class="ml-2 text-sm font-medium text-gray-600">Package Selection</span>
                </div>
                <div class="w-8 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                    <span class="ml-2 text-sm font-medium text-gray-600">Confirmation</span>
                </div>
            </div>
        </div>

        <!-- Booking Form -->
        <form action="<?php echo e(route('catering.booking.step1')); ?>" method="POST" id="catering-form" class="bg-white rounded-lg shadow-md p-6">
            <?php echo csrf_field(); ?>

            <!-- Event Details -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Event Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="catering_event_type_id" class="block text-sm font-medium text-gray-700 mb-1">Event Type *</label>
                        <select id="catering_event_type_id" name="catering_event_type_id" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            <option value="">Select Event Type</option>
                            <?php $__currentLoopData = $eventTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $eventType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($eventType->id); ?>" <?php echo e((old('catering_event_type_id') ?? $selectedEventType) == $eventType->id ? 'selected' : ''); ?>>
                                <?php echo e($eventType->name); ?>

                            </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['catering_event_type_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="event_date" class="block text-sm font-medium text-gray-700 mb-1">Event Date *</label>
                        <input type="date" id="event_date" name="event_date" value="<?php echo e(old('event_date') ?? $eventDate); ?>" required
                               min="<?php echo e(date('Y-m-d', strtotime('+2 days'))); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        <?php $__errorArgs = ['event_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="guest_count" class="block text-sm font-medium text-gray-700 mb-1">Number of Guests *</label>
                        <input type="number" id="guest_count" name="guest_count" value="<?php echo e(old('guest_count') ?? $guestCount); ?>" required
                               min="1" max="1000"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        <?php $__errorArgs = ['guest_count'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>



            <!-- Additional Services -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Additional Services</h2>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" name="needs_setup_service" value="1" <?php echo e((old('needs_setup_service') ?? $additionalServices['needs_setup_service']) ? 'checked' : ''); ?>

                               class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                        <span class="ml-2 text-sm text-gray-700">Setup Service</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="needs_cleanup_service" value="1" <?php echo e((old('needs_cleanup_service') ?? $additionalServices['needs_cleanup_service']) ? 'checked' : ''); ?>

                               class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                        <span class="ml-2 text-sm text-gray-700">Cleanup Service</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="needs_serving_staff" value="1" <?php echo e((old('needs_serving_staff') ?? $additionalServices['needs_serving_staff']) ? 'checked' : ''); ?>

                               class="rounded border-gray-300 text-orange-600 focus:ring-orange-500" id="needs_serving_staff">
                        <span class="ml-2 text-sm text-gray-700">Professional Serving Staff</span>
                    </label>

                    <div id="staff_count_container" class="ml-6 <?php echo e((old('needs_serving_staff') ?? $additionalServices['needs_serving_staff']) ? '' : 'hidden'); ?>">
                        <label for="serving_staff_count" class="block text-sm font-medium text-gray-700 mb-1">Number of Staff</label>
                        <input type="number" id="serving_staff_count" name="serving_staff_count" value="<?php echo e(old('serving_staff_count') ?? $additionalServices['serving_staff_count']); ?>"
                               min="0" max="20"
                               class="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                    </div>
                </div>
            </div>



            <!-- Navigation Buttons -->
            <div class="flex justify-between items-center">
                <a href="<?php echo e(route('catering.index')); ?>" class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back
                </a>
                <button type="submit" class="bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors">
                    Next Step <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Show/hide staff count input
    $('#needs_serving_staff').change(function() {
        if ($(this).is(':checked')) {
            $('#staff_count_container').removeClass('hidden');
        } else {
            $('#staff_count_container').addClass('hidden');
            $('#serving_staff_count').val(0);
        }
    });

    // Initialize if values are pre-filled
    if ($('#needs_serving_staff').is(':checked')) {
        $('#staff_count_container').removeClass('hidden');
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\r56t7yihu\food-ordering-app\resources\views/catering/booking.blade.php ENDPATH**/ ?>